{"name": "ai-tmai-tcamp-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"antd": "^5.22.5", "axios": "^1.7.9", "crypto-js": "^4.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.1.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/crypto-js": "^4.2.2", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}