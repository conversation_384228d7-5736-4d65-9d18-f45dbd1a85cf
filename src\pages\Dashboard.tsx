import React from 'react';
import { Layout, Card, Typography, Button, Space, Avatar, Dropdown, MenuProps } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import { useAuthStore } from '../utils/authStore';
import { useNavigate } from 'react-router-dom';
import { logout } from '../services/auth';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { user, logout: clearAuth } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      clearAuth();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // 即使登出接口失败，也清除本地状态
      clearAuth();
      navigate('/login');
    }
  };

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px', 
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          TMAI-TCamp 管理系统
        </Title>
        
        <Space>
          <Text>欢迎，{user?.name || user?.username}</Text>
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <Avatar 
              style={{ backgroundColor: '#1890ff', cursor: 'pointer' }} 
              icon={<UserOutlined />}
            />
          </Dropdown>
        </Space>
      </Header>

      <Content style={{ padding: '24px', background: '#f0f2f5' }}>
        <div style={{ maxWidth: 1200, margin: '0 auto' }}>
          <Title level={2}>仪表板</Title>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
            gap: '24px',
            marginTop: '24px'
          }}>
            <Card title="用户信息" bordered={false}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>用户ID：</Text>
                  <Text>{user?.uid}</Text>
                </div>
                <div>
                  <Text strong>用户名：</Text>
                  <Text>{user?.username}</Text>
                </div>
                <div>
                  <Text strong>姓名：</Text>
                  <Text>{user?.name}</Text>
                </div>
                <div>
                  <Text strong>租户ID：</Text>
                  <Text>{user?.tenant_id}</Text>
                </div>
              </Space>
            </Card>

            <Card title="系统状态" bordered={false}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>登录状态：</Text>
                  <Text style={{ color: '#52c41a' }}>已登录</Text>
                </div>
                <div>
                  <Text strong>Token状态：</Text>
                  <Text style={{ color: '#52c41a' }}>有效</Text>
                </div>
                <div>
                  <Text strong>登录时间：</Text>
                  <Text>{new Date().toLocaleString()}</Text>
                </div>
              </Space>
            </Card>

            <Card title="快速操作" bordered={false}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button type="primary" block>
                  查看个人信息
                </Button>
                <Button block>
                  修改密码
                </Button>
                <Button danger block onClick={handleLogout}>
                  退出登录
                </Button>
              </Space>
            </Card>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default Dashboard;
